const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
    email: '<EMAIL>',
    password: 'Password123'
};

let authToken = null;

async function login() {
    try {
        console.log('🔐 Logging in user...');
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
            email: TEST_USER.email,
            password: TEST_USER.password
        });

        authToken = response.data.token;
        console.log('✅ Login successful');
        return response.data;
    } catch (error) {
        console.error('❌ Login failed:', error.response?.data || error.message);
        throw error;
    }
}

async function testStatusEndpoint() {
    try {
        console.log('📊 Testing status endpoint...');
        console.log('🔗 URL:', `${BASE_URL}/api/auto-application/status`);
        
        const response = await axios.get(`${BASE_URL}/api/auto-application/status`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Status endpoint response:', JSON.stringify(response.data, null, 2));
        console.log('📋 Response headers:', response.headers);
        return response.data;
    } catch (error) {
        console.error('❌ Status endpoint failed:', error.response?.data || error.message);
        console.error('📋 Error response headers:', error.response?.headers);
        throw error;
    }
}

async function main() {
    try {
        // Login first
        await login();
        
        // Test status endpoint
        await testStatusEndpoint();
        
        console.log('\n🎉 All tests completed successfully!');
    } catch (error) {
        console.error('\n💥 Test failed:', error.message);
        process.exit(1);
    }
}

main();