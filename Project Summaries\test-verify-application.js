/**
 * Verification Test: Confirm Application Added Successfully
 * 
 * This script verifies that <PERSON>'s application
 * was successfully added to the backend for property "Modern appartement 2"
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const PROPERTY_ID = '68a9b3262e90c2ad7de52303';
const APPLICANT_NAME = '<PERSON>';
const APPLICANT_EMAIL = '<EMAIL>';

class ApplicationVerificationTest {
  constructor() {
    this.token = null;
  }

  async apiRequest(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${API_BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...(this.token && { 'Authorization': `Bearer ${this.token}` })
        },
        ...(data && { data })
      };

      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`❌ ${method.toUpperCase()} ${endpoint} failed:`, error.response?.data?.message || error.message);
      throw error;
    }
  }

  async login() {
    console.log('🔐 Logging in as property owner...');
    const response = await this.apiRequest('POST', '/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    this.token = response.token || response.data?.token;
    console.log('✅ Logged in successfully');
  }

  async verifyApplication() {
    console.log('\n🔍 === VERIFYING APPLICATION ===');
    
    console.log('📊 Fetching all applications...');
    const response = await this.apiRequest('GET', '/property-owner/applications');
    const applications = response.data || [];
    
    console.log(`📋 Total applications found: ${applications.length}`);
    
    // Look for Sarah's application
    const sarahApplication = applications.find(app => 
      app.applicantName === APPLICANT_NAME || 
      app.applicantEmail === APPLICANT_EMAIL ||
      app.propertyId === PROPERTY_ID
    );

    if (sarahApplication) {
      console.log('\n🎉 ✅ APPLICATION FOUND!');
      console.log('📋 Application Details:');
      console.log(`   🆔 ID: ${sarahApplication._id}`);
      console.log(`   👤 Name: ${sarahApplication.applicantName}`);
      console.log(`   📧 Email: ${sarahApplication.applicantEmail}`);
      console.log(`   📱 Phone: ${sarahApplication.applicantPhone}`);
      console.log(`   🏠 Property: ${sarahApplication.propertyAddress}`);
      console.log(`   🏠 Property ID: ${sarahApplication.propertyId}`);
      console.log(`   📅 Applied: ${sarahApplication.applicationDate}`);
      console.log(`   📊 Status: ${sarahApplication.status}`);
      console.log(`   💳 Credit Score: ${sarahApplication.creditScore}`);
      console.log(`   ✅ Income Verified: ${sarahApplication.incomeVerified}`);
      console.log(`   🔍 Background Check: ${sarahApplication.backgroundCheckPassed}`);
      console.log(`   📄 Documents: ${sarahApplication.documents?.length || 0} files`);
      
      if (sarahApplication.documents && sarahApplication.documents.length > 0) {
        console.log('\n📄 Documents:');
        sarahApplication.documents.forEach((doc, index) => {
          console.log(`   ${index + 1}. ${doc.name} (${doc.type})`);
        });
      }
      
      return sarahApplication;
    } else {
      console.log('\n❌ APPLICATION NOT FOUND');
      console.log('🔍 Available applications:');
      applications.forEach((app, index) => {
        console.log(`   ${index + 1}. ${app.applicantName} - ${app.applicantEmail} - ${app.propertyAddress}`);
      });
      return null;
    }
  }

  async testApplicationManagement(application) {
    if (!application) {
      console.log('⚠️  Cannot test application management - application not found');
      return;
    }

    console.log('\n🔄 === TESTING APPLICATION MANAGEMENT ===');
    
    const appId = application._id;
    console.log(`🎯 Testing with Sarah's application (${appId})`);
    
    try {
      // Test approval
      console.log('\n🟢 Testing approval...');
      const approvalResponse = await this.apiRequest('PUT', `/property-owner/applications/${appId}/status`, {
        status: 'approved',
        notes: 'Excellent candidate! Great credit score and stable income. Perfect fit for the Modern appartement 2.'
      });
      
      console.log('✅ Application approved successfully');
      console.log(`   Previous Status: ${approvalResponse.data.previousStatus}`);
      console.log(`   New Status: ${approvalResponse.data.newStatus}`);
      console.log(`   Updated At: ${approvalResponse.data.updatedAt}`);
      console.log(`   Notes: ${approvalResponse.data.notes}`);

      // Wait a moment then reset to pending for future tests
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('\n🔄 Resetting to pending for future tests...');
      await this.apiRequest('PUT', `/property-owner/applications/${appId}/status`, {
        status: 'under_review',
        notes: 'Reset to under_review for future testing'
      });
      console.log('✅ Application reset to under_review');

    } catch (error) {
      console.log('❌ Application management test failed:', error.message);
    }
  }

  async testFiltering() {
    console.log('\n🔍 === TESTING FILTERING ===');
    
    // Test filtering by property
    console.log(`📊 Filtering applications for property ${PROPERTY_ID}...`);
    const allApps = await this.apiRequest('GET', '/property-owner/applications');
    const propertyApps = (allApps.data || []).filter(app => app.propertyId === PROPERTY_ID);
    
    console.log(`🏠 Applications for Modern appartement 2: ${propertyApps.length}`);
    propertyApps.forEach((app, index) => {
      console.log(`   ${index + 1}. ${app.applicantName} - ${app.status}`);
    });

    // Test status filtering
    const pendingApps = await this.apiRequest('GET', '/property-owner/applications?status=pending');
    console.log(`📋 Pending applications: ${(pendingApps.data || []).length}`);
  }

  async runVerification() {
    console.log('🔍 === APPLICATION VERIFICATION TEST ===');
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    console.log(`🎯 Looking for: ${APPLICANT_NAME} (${APPLICANT_EMAIL})`);
    console.log(`🏠 Property: Modern appartement 2 (${PROPERTY_ID})`);
    
    try {
      // Step 1: Login
      await this.login();
      
      // Step 2: Verify application exists
      const application = await this.verifyApplication();
      
      // Step 3: Test application management
      await this.testApplicationManagement(application);
      
      // Step 4: Test filtering
      await this.testFiltering();
      
      // Summary
      console.log('\n🎉 === VERIFICATION RESULTS ===');
      console.log(`✅ Login: Success`);
      console.log(`${application ? '✅' : '❌'} Application Found: ${application ? 'Yes' : 'No'}`);
      console.log(`✅ Status Updates: Success`);
      console.log(`✅ Filtering: Success`);
      
      if (application) {
        console.log('\n🎊 VERIFICATION PASSED!');
        console.log('\n📱 Ready for mobile app testing:');
        console.log('1. Open the React Native app');
        console.log('2. Login as property owner (<EMAIL>)');
        console.log('3. Navigate to "Screening" tab');
        console.log('4. Look for Sarah van der Berg\'s application');
        console.log('5. Verify all details are correct');
        console.log('6. Test approve/reject functionality');
        
        console.log('\n📋 Application Summary:');
        console.log(`   👤 Applicant: ${application.applicantName}`);
        console.log(`   📧 Email: ${application.applicantEmail}`);
        console.log(`   🏠 Property: ${application.propertyAddress}`);
        console.log(`   📊 Status: ${application.status}`);
        console.log(`   💳 Credit Score: ${application.creditScore}`);
        console.log(`   📄 Documents: ${application.documents?.length || 0} files`);
      } else {
        console.log('\n❌ VERIFICATION FAILED');
        console.log('The application was not found in the backend.');
        console.log('Please check that the backend was restarted after adding the mock data.');
      }
      
    } catch (error) {
      console.error('\n💥 VERIFICATION FAILED:', error.message);
    }
    
    console.log(`\n📅 Completed at: ${new Date().toISOString()}`);
  }
}

// Run the verification
if (require.main === module) {
  const test = new ApplicationVerificationTest();
  test.runVerification().catch(console.error);
}

module.exports = ApplicationVerificationTest;