#!/usr/bin/env node

/**
 * Test Login Script for ZakMakelaar Backend
 * 
 * This script tests if the password reset was successful by attempting to login
 * Usage: node scripts/test-login.js
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const config = require('../src/config/config');
const User = require('../src/models/User');

async function testLogin() {
  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(config.mongoURI);
    console.log('✅ Connected to MongoDB successfully');

    const email = '<EMAIL>';
    const password = 'testpassword123';

    console.log(`🔍 Testing login for: ${email}`);

    // Find the user
    const user = await User.findOne({ email }).select('+password');
    
    if (!user) {
      console.error('❌ User not found');
      process.exit(1);
    }

    console.log(`👤 Found user: ${user.email} (ID: ${user._id})`);
    console.log(`📋 Role: ${user.role}`);

    // Test password
    console.log('🔐 Testing password...');
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (isPasswordValid) {
      console.log('✅ Password is valid! Login would succeed.');
      console.log('');
      console.log('🎉 SUCCESS: Password reset was successful!');
      console.log('📧 Login credentials:');
      console.log(`   Email: ${email}`);
      console.log(`   Password: ${password}`);
    } else {
      console.error('❌ Password is invalid! Login would fail.');
      console.error('');
      console.error('💡 The password reset may not have worked correctly.');
    }

  } catch (error) {
    console.error('❌ Error testing login:', error.message);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('🔌 MongoDB connection closed');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err.message);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err.message);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  testLogin();
}

module.exports = { testLogin };
