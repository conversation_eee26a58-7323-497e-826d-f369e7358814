/**
 * Script: Verify Database State
 * 
 * This script provides MongoDB commands to check the exact state of:
 * 1. The target property ownership
 * 2. The application document
 * 3. The relationship between them
 */

console.log('🗄️  === DATABASE VERIFICATION COMMANDS ===');
console.log('');
console.log('Run these commands in MongoDB Compass or MongoDB shell:');
console.log('');

console.log('🔍 1. CHECK TARGET PROPERTY OWNERSHIP:');
console.log('```javascript');
console.log('db.properties.findOne(');
console.log('  { _id: ObjectId("68a9b3262e90c2ad7de52303") },');
console.log('  { title: 1, "owner.userId": 1, status: 1 }');
console.log(')');
console.log('```');
console.log('Expected result: owner.userId should be ObjectId("68a9cd0c01eb3f61f005a71c")');
console.log('');

console.log('🔍 2. CHECK ALL PROPERTIES OWNED BY USER:');
console.log('```javascript');
console.log('db.properties.find(');
console.log('  { "owner.userId": ObjectId("68a9cd0c01eb3f61f005a71c") },');
console.log('  { _id: 1, title: 1, status: 1 }');
console.log(')');
console.log('```');
console.log('Expected result: Should include the target property 68a9b3262e90c2ad7de52303');
console.log('');

console.log('🔍 3. CHECK TARGET APPLICATION:');
console.log('```javascript');
console.log('db.applications.findOne(');
console.log('  { _id: ObjectId("68a9d0b27cc6c69855b7b593") },');
console.log('  { ');
console.log('    "applicant.snapshot.email": 1,');
console.log('    "property.propertyId": 1,');
console.log('    status: 1,');
console.log('    createdAt: 1');
console.log('  }');
console.log(')');
console.log('```');
console.log('Expected result: property.propertyId should be ObjectId("68a9b3262e90c2ad7de52303")');
console.log('');

console.log('🔍 4. CHECK ALL APPLICATIONS FOR TARGET PROPERTY:');
console.log('```javascript');
console.log('db.applications.find(');
console.log('  { "property.propertyId": ObjectId("68a9b3262e90c2ad7de52303") },');
console.log('  { ');
console.log('    "applicant.snapshot.email": 1,');
console.log('    "applicant.snapshot.name": 1,');
console.log('    status: 1,');
console.log('    createdAt: 1');
console.log('  }');
console.log(')');
console.log('```');
console.log('Expected result: Should show the <NAME_EMAIL>');
console.log('');

console.log('🔧 5. IF PROPERTY OWNERSHIP IS STILL WRONG, FIX IT:');
console.log('```javascript');
console.log('db.properties.updateOne(');
console.log('  { _id: ObjectId("68a9b3262e90c2ad7de52303") },');
console.log('  { $set: { "owner.userId": ObjectId("68a9cd0c01eb3f61f005a71c") } }');
console.log(')');
console.log('```');
console.log('');

console.log('🔧 6. IF APPLICATION PROPERTY ID IS WRONG, FIX IT:');
console.log('```javascript');
console.log('db.applications.updateOne(');
console.log('  { _id: ObjectId("68a9d0b27cc6c69855b7b593") },');
console.log('  { $set: { "property.propertyId": ObjectId("68a9b3262e90c2ad7de52303") } }');
console.log(')');
console.log('```');
console.log('');

console.log('🎯 EXPECTED FINAL STATE:');
console.log('- Property 68a9b3262e90c2ad7de52303 owned by user 68a9cd0c01eb3f61f005a71c');
console.log('- Application 68a9d0b27cc6c69855b7b593 linked to property 68a9b3262e90c2ad7de52303');
console.log('- <NAME_EMAIL> should appear in property owner dashboard');
console.log('');

console.log('📋 AFTER RUNNING THESE COMMANDS:');
console.log('1. Restart the backend server (if needed)');
console.log('2. Run: node test-real-database.js');
console.log('3. The application should appear!');
console.log('');

console.log('🚀 QUICK TEST COMMAND:');
console.log('After fixing the database, test with:');
console.log('curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/api/property-owner/applications');
console.log('');

// Also provide a simple test
console.log('💡 OR RUN THIS SIMPLE TEST:');

const axios = require('axios');

async function quickTest() {
  try {
    console.log('🔐 Testing login...');
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const token = loginResponse.data.token || loginResponse.data.data?.token;
    console.log('✅ Login successful');
    
    console.log('📊 Testing applications endpoint...');
    const appsResponse = await axios.get('http://localhost:3000/api/property-owner/applications', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const applications = appsResponse.data.data || [];
    console.log(`📋 Found ${applications.length} applications`);
    
    if (applications.length > 0) {
      console.log('🎉 SUCCESS! Applications found:');
      applications.forEach((app, index) => {
        console.log(`   ${index + 1}. ${app.applicantName} (${app.applicantEmail}) - ${app.status}`);
      });
    } else {
      console.log('❌ No applications found - database fixes needed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

if (require.main === module) {
  console.log('Running quick test in 3 seconds...');
  setTimeout(quickTest, 3000);
}