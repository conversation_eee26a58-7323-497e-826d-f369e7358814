// Scraping configuration and constants

const SCRAPING_CONFIG = {
  // Browser settings
  browser: {
    maxBrowsers: 2,
    headless: process.env.NODE_ENV === "production",
    timeout: 60000,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-infobars",
      "--window-position=0,0",
      "--ignore-certificate-errors",
      "--disable-background-timer-throttling",
      "--disable-backgrounding-occluded-windows",
      "--disable-renderer-backgrounding",
      "--disable-dev-shm-usage",
      "--disable-extensions",
      "--disable-blink-features=AutomationControlled",
      // Additional Docker-optimized arguments
      "--disable-gpu",
      "--disable-software-rasterizer",
      "--disable-background-networking",
      "--disable-default-apps",
      "--disable-sync",
      "--no-first-run",
      "--no-default-browser-check",
      "--single-process",
      "--disable-features=VizDisplayCompositor",
    ],
  },

  // Retry settings
  retry: {
    maxRetries: 3,
    baseDelay: 5000, // 5 seconds
    retryableErrors: [
      "TimeoutError",
      "NetworkError",
      "ECONNRESET",
      "ENOTFOUND",
      "ECONNREFUSED",
      "ERR_NETWORK_CHANGED",
      "ERR_INTERNET_DISCONNECTED",
    ],
  },

  // Anti-detection settings
  stealth: {
    userAgents: [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
      "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    ],
    viewports: [
      { width: 1920, height: 1080 },
      { width: 1366, height: 768 },
      { width: 1440, height: 900 },
      { width: 1536, height: 864 },
      { width: 1280, height: 720 },
    ],
    delays: {
      min: 2000,
      max: 8000,
      scroll: 100,
    },
  },

  // Site-specific settings
  sites: {
    funda: {
      baseUrl: "https://www.funda.nl",
      searchUrls: [
        "https://www.funda.nl/huur/heel-nederland/",
        "https://www.funda.nl/huur/heel-nederland/?selected_area=%5B%22nederland%22%5D&object_type=%5B%22house%22%5D",
        "https://www.funda.nl/huur/heel-nederland/?selected_area=%5B%22nederland%22%5D&object_type=%5B%22apartment%22%5D",
      ],
      selectors: {
        listContainer: 'ol[data-test-id="search-results"] > li',
        title: 'h2[data-test-id="street-name-house-number"]',
        price: 'p[data-test-id="price"]',
        location: 'p[data-test-id="postal-code-city"]',
        link: 'a[data-test-id="object-image-link"]',
        jsonLd:
          'script[type="application/ld+json"][data-hid="result-list-metadata"]',
      },
      cookies: [
        {
          name: "OptanonAlertBoxClosed",
          domain: ".funda.nl",
          httpOnly: false,
          secure: true,
        },
        {
          name: "OptanonConsent",
          domain: ".funda.nl",
          httpOnly: false,
          secure: true,
        },
      ],
    },
    pararius: {
      baseUrl: "https://www.pararius.nl",
      searchUrls: [
        "https://www.pararius.nl/huurwoningen/nederland",
        "https://www.pararius.nl/huurwoningen/amsterdam",
        "https://www.pararius.nl/huurwoningen/rotterdam",
        "https://www.pararius.nl/huurwoningen/den-haag",
        "https://www.pararius.nl/huurwoningen/utrecht",
      ],
      selectors: {
        listContainer: ".search-list__item--listing",
        title: ".listing-search-item__title",
        price: ".listing-search-item__price",
        location: ".listing-search-item__location",
        link: "a",
      },
    },
    huurwoningen: {
      baseUrl: "https://www.huurwoningen.nl",
      searchUrls: [
        "https://www.huurwoningen.nl/in/amsterdam/",
        "https://www.huurwoningen.nl/in/rotterdam/",
        "https://www.huurwoningen.nl/in/den-haag/",
        "https://www.huurwoningen.nl/in/utrecht/",
        "https://www.huurwoningen.nl/in/eindhoven/",
        "https://www.huurwoningen.nl/in/groningen/",
      ],
      selectors: {
        listContainer: "main section > div > div",
        title: "h3 a, h2 a",
        price: "div:contains('€')",
        location:
          "div:contains('Amsterdam'), div:contains('Rotterdam'), div:contains('Den Haag'), div:contains('Utrecht'), div:contains('Eindhoven'), div:contains('Groningen')",
        link: "a[href*='/huren/']",
        size: "li:contains('m²')",
        rooms: "li:contains('kamer')",
        year: "li:contains('19'), li:contains('20')",
        interior:
          "li:contains('Kaal'), li:contains('Gestoffeerd'), li:contains('Gemeubileerd')",
      },
      cookies: [
        {
          name: "cookie_consent",
          domain: ".huurwoningen.nl",
          httpOnly: false,
          secure: true,
        },
      ],
    },
  },

  // Data validation rules
  validation: {
    required: ["title", "url", "location"],
    maxTitleLength: 200,
    maxLocationLength: 100,
    pricePatterns: {
      euro: /€\s*[\d.,]+/,
      numeric: /(\d+(?:\.\d+)?)/,
    },
  },
};

module.exports = SCRAPING_CONFIG;
