// MongoDB script to reset user password
// Usage: mongosh --username admin --password password123 --authenticationDatabase admin zak<PERSON>aar scripts/reset-password.js

const email = '<EMAIL>';
const newPasswordHash = '$2b$10$2vliVmpMnRqknhyGWCH0rOb3fQa/v0dKPOgBP.dMfs0WOAEjt7vwC'; // testpassword123

print('🔍 Looking for user with email:', email);

const user = db.users.findOne({email: email});
if (!user) {
  print('❌ User not found with email:', email);
  quit(1);
}

print('👤 Found user:', user.email, '(ID:', user._id, ')');
print('📋 Current role:', user.role);

print('💾 Updating password...');

const result = db.users.updateOne(
  {email: email},
  {
    $set: {
      password: newPasswordHash,
      'security.lastPasswordChange': new Date(),
      'security.loginAttempts.count': 0
    },
    $unset: {
      'security.loginAttempts.lockedUntil': 1
    }
  }
);

if (result.modifiedCount === 1) {
  print('✅ Password reset successfully!');
  print('📧 User can now login with:');
  print('   Email:', email);
  print('   Password: testpassword123');
} else {
  print('❌ Failed to update password');
  quit(1);
}

print('🔍 Verifying update...');
const updatedUser = db.users.findOne({email: email});
if (updatedUser.password === newPasswordHash) {
  print('✅ Password hash verified in database!');
} else {
  print('❌ Password hash verification failed!');
}
