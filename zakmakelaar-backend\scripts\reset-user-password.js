#!/usr/bin/env node

/**
 * Password Reset Utility for ZakMakelaar Backend
 *
 * This script allows administrators to reset user passwords directly in the database.
 * Usage: node scripts/reset-user-password.js <email> <new-password>
 *
 * Example: node scripts/reset-user-password.js <EMAIL> newpassword123
 */

const mongoose = require("mongoose");
const bcrypt = require("bcrypt");
const config = require("../src/config/config");

// Import User model
const User = require("../src/models/User");

async function resetUserPassword(email, newPassword) {
  try {
    // Connect to MongoDB
    console.log("🔌 Connecting to MongoDB...");
    console.log("🔗 MongoDB URI:", config.mongoURI);
    await mongoose.connect(config.mongoURI);
    console.log("✅ Connected to MongoDB successfully");

    // Find the user
    console.log(`🔍 Looking for user with email: ${email}`);
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      console.error("❌ User not found with email:", email);
      process.exit(1);
    }

    console.log(`👤 Found user: ${user.email} (ID: ${user._id})`);
    console.log(`📋 Current role: ${user.role}`);
    console.log(`📅 Created: ${user.createdAt}`);

    // Validate new password
    if (!newPassword || newPassword.length < 6) {
      console.error("❌ New password must be at least 6 characters long");
      process.exit(1);
    }

    // Hash the new password
    console.log("🔐 Hashing new password...");
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update the user's password
    console.log("💾 Updating password in database...");
    user.password = hashedPassword;

    // Update security fields if they exist
    if (user.security) {
      user.security.lastPasswordChange = new Date();
      // Reset login attempts if user was locked
      if (user.security.loginAttempts) {
        user.security.loginAttempts.count = 0;
        user.security.loginAttempts.lockedUntil = undefined;
      }
    }

    // Save the user (this will trigger the pre-save middleware, but we're setting the password directly)
    // We need to bypass the pre-save middleware since we already hashed the password
    await User.updateOne(
      { _id: user._id },
      {
        $set: {
          password: hashedPassword,
          "security.lastPasswordChange": new Date(),
          "security.loginAttempts.count": 0,
          $unset: { "security.loginAttempts.lockedUntil": 1 },
        },
      }
    );

    console.log("✅ Password reset successfully!");
    console.log("📧 User can now login with:");
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${newPassword}`);

    // Verify the password works
    console.log("🔍 Verifying new password...");
    const updatedUser = await User.findById(user._id).select("+password");
    const isPasswordValid = await bcrypt.compare(
      newPassword,
      updatedUser.password
    );

    if (isPasswordValid) {
      console.log("✅ Password verification successful!");
    } else {
      console.error("❌ Password verification failed!");
    }
  } catch (error) {
    console.error("❌ Error resetting password:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log("🔌 MongoDB connection closed");
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.length !== 2) {
    console.log(
      "📖 Usage: node scripts/reset-user-password.js <email> <new-password>"
    );
    console.log(
      "📖 Example: node scripts/reset-user-password.js <EMAIL> newpassword123"
    );
    process.exit(1);
  }

  const [email, newPassword] = args;

  console.log("🚀 Starting password reset process...");
  console.log(`📧 Target email: ${email}`);
  console.log(`🔐 New password length: ${newPassword.length} characters`);
  console.log("");

  await resetUserPassword(email, newPassword);
}

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  console.error("❌ Unhandled Promise Rejection:", err.message);
  process.exit(1);
});

// Handle uncaught exceptions
process.on("uncaughtException", (err) => {
  console.error("❌ Uncaught Exception:", err.message);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = { resetUserPassword };
